<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5410281e-0c6e-408a-b241-a40d400258d4" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/lib/menus/my_app_bar.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/menus/my_app_bar.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/contacts/contacts.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/contacts/contacts.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/dashboard_widgets/status_widget/StatusComponent.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/dashboard_widgets/status_widget/StatusComponent.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/employees/edit_employee.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/employees/edit_employee.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/employees/employee_types.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/employees/employee_types.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/employees/employees.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/employees/employees.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/locations/edit_location.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/locations/edit_location.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/locations/locations.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/locations/locations.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/map/map.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/map/map.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/map/widgets/total_counts.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/map/widgets/total_counts.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/punchCards/punch_cards.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/punchCards/punch_cards.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/punchCards/view_punch_card.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/punchCards/view_punch_card.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/punchCards/web_edit_punch_card.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/punchCards/web_edit_punch_card.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/web/schedules/schedules.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/web/schedules/schedules.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/widgets/calendar/calendar_week_widget.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/widgets/calendar/calendar_week_widget.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/widgets/card_page.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/widgets/card_page.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/widgets/menu/aligned_popup_menu_button.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/widgets/menu/aligned_popup_menu_button.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/widgets/menu/my_tab_bar.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/widgets/menu/my_tab_bar.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/widgets/progress_bar/garbgae.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/widgets/progress_bar/progress_bar.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/widgets/progress_bar/progress_bar.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/widgets/tables_global.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/widgets/tables_global.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/pubspec.lock" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2sPcZkawUKvGbiPzBfMD7VNifeP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;AnalyzeApkAction.lastApkPath&quot;: &quot;C:/Users/<USER>/code/punch/the_punch_flutter&quot;,
    &quot;Flutter.main.dart.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;alerts-page&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;io.flutter.reload.alreadyRun&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/code/punch/the_punch_flutter&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDKs&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.build.tools.auto.reload&quot;: &quot;ALL&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;instant.run&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="main.dart" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5410281e-0c6e-408a-b241-a40d400258d4" name="Changes" comment="" />
      <created>1738360708103</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1738360708103</updated>
    </task>
    <servers />
  </component>
</project>