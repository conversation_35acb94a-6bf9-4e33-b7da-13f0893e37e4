import 'dart:async';
import 'package:flutter/material.dart';
import '../../helpers/color_helper.dart';

class MyTabBar<T> extends StatefulWidget {
  final List<TabButton<T>> children;
  final Widget Function(BuildContext context, T value) itemBuilder;
  final Function(T value)? pageChanged;

  const MyTabBar(
      {super.key,
      required this.itemBuilder,
      required this.children,
      this.pageChanged});

  @override
  State<MyTabBar<T>> createState() => _MyTabBarState<T>();
}

class _MyTabBarState<T> extends State<MyTabBar<T>> {
  final controller = PageController();
  int page = 0;

  @override
  void initState() {
    super.initState();
    controller.addListener(() {
      if (controller.page == null) return;
      final newPage = controller.page!.toInt();
      if (controller.page == newPage.toDouble()) {
        if (newPage >= 0 && newPage < widget.children.length) {
          setState(() => page = newPage);
          widget.pageChanged?.call(widget.children[page].value);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final selectedStyle = OutlinedButton.styleFrom(
        side: BorderSide(
      color: ColorHelper.thePunchRed(),
    ));

    final unSelectedStyle = OutlinedButton.styleFrom(
        side: BorderSide(
      color: ColorHelper.thePunchGray(),
    ));

    final children0 = <Widget>[];
    for (var i = 0; i < widget.children.length; i++) {
      children0.add(Padding(
        padding: const EdgeInsets.all(2),
        child: ConstrainedBox(
          constraints: const BoxConstraints(minHeight: 40, maxWidth: 140 ),
          child: OutlinedButton(
            style: (i == page) ? selectedStyle : unSelectedStyle,
            onPressed: () {
              if ((page - i).abs() > 1) {
                controller.jumpToPage(i);
              } else {
                controller.animateToPage(i,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.ease);
              }
              setState(() => page = i);
            },
            child: widget.children[i],
          ),
        ),
      ));
    }
    return Column(
      children: [
        Row(
          //mainAxisSize: MainAxisSize.min, 
        // crossAxisAlignment: CrossAxisAlignment.stretch,
         mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: children0),
        SizedBox(
          height: 20,
        ),
        SizedBox(
          height: 150, // Adjust the height as needed
          child: PageView.builder(
              controller: controller,
              itemCount: widget.children.length,
              itemBuilder: (context, index) =>
                  widget.itemBuilder(context, widget.children[index].value)),
        )
      ],
    );
  }
}

class TabButton<T> extends StatelessWidget {
  final T value;
  final Widget child;

  const TabButton({super.key, required this.value, required this.child});
  @override
  Widget build(BuildContext context) => child;
}
